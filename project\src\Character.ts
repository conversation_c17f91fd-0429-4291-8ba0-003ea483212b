export class Character {
    name: string;
    maxHealth: number;
    health: number;
    isAlive: boolean;
    maxStamina: number;
    stamina: number;
    isTired: boolean;

    constructor(name: string, maxHealth: number) {
        this.name = name;
        this.maxHealth = maxHealth;
        this.health = maxHealth;
        this.isAlive = true;
        this.maxStamina = 100;
        this.stamina = 100;
        this.isTired = false;
    }

    takeDamage(amount: number) {
        if (!this.isAlive) return;
        this.health -= amount;
        if (this.health <= 0) {
            this.health = 0;
            this.isAlive = false;
        }
    }

    heal(amount: number) {
        if (!this.isAlive) return;
        this.health += amount;
        if (this.health > this.maxHealth) {
            this.health = this.maxHealth;
        }
    }

    revive() {
        if (this.isAlive) return;
        this.health = this.maxHealth;
        this.isAlive = true;
    }

    get staminaPercent() {
        return this.stamina / this.maxStamina;
    }

    consumeStamina(amount: number) {
        if (this.stamina <= 0) {
            this.stamina = 0;
            this.isTired = true;
            return;
        }
        this.stamina -= amount;
        if (this.stamina < 0) this.stamina = 0;
        if (this.stamina === 0) this.isTired = true;
    }

    recoverStamina(amount: number) {
        this.stamina += amount;
        if (this.stamina > this.maxStamina) this.stamina = this.maxStamina;
        if (this.stamina > 0) this.isTired = false;
    }
} 