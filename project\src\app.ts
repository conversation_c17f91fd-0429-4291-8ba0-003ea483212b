import * as BABYLON from 'babylonjs';
import 'babylonjs-loaders';
import { CharacterController } from 'babylonjs-charactercontroller';
import { Character } from './Character';
import { HealthBar } from './HealthBar';
import { NavMeshManager } from './navmesh';

export class App {
    
    private engine: BABYLON.Engine;
    private scene: BABYLON.Scene;


    public static CreateScene(engine: BABYLON.Engine, canvas: HTMLCanvasElement): BABYLON.Scene {
    const scene = new BABYLON.Scene(engine);

    // Gravity for the scene
    scene.gravity = new BABYLON.Vector3(0, -2, 0);

    // Top-Down 固定摄像头
    const camera = new BABYLON.ArcRotateCamera("camera1", -Math.PI / 2, 0, 200, new BABYLON.Vector3(0, 0, 0), scene);
    camera.setPosition(new BABYLON.Vector3(0, 200, 0));
    camera.target = new BABYLON.Vector3(0, 0, 0);
    camera.attachControl(canvas, true);
    camera.lowerBetaLimit = 0;
    camera.upperBetaLimit = 0;
    camera.lowerAlphaLimit = -Math.PI / 2;
    camera.upperAlphaLimit = -Math.PI / 2;
    camera.lowerRadiusLimit = 50;
    camera.upperRadiusLimit = 600;
    camera.panningSensibility = 0;
    camera.wheelPrecision = 1.5;
    camera.allowUpsideDown = false;

    // Skybox
	var skybox = BABYLON.MeshBuilder.CreateBox("skyBox", {size:1000.0}, scene);
	var skyboxMaterial = new BABYLON.StandardMaterial("skyBox", scene);
	skyboxMaterial.backFaceCulling = false;
	skyboxMaterial.reflectionTexture = new BABYLON.CubeTexture("scene/textures/skybox", scene);
	skyboxMaterial.reflectionTexture.coordinatesMode = BABYLON.Texture.SKYBOX_MODE;
	skyboxMaterial.diffuseColor = new BABYLON.Color3(0, 0, 0);
	skyboxMaterial.specularColor = new BABYLON.Color3(0, 0, 0);
    skybox.material = skyboxMaterial;

 

    // Ambient light
    const light = new BABYLON.HemisphericLight("light", new BABYLON.Vector3(0, 1, 1), scene);
    light.intensity = 1;

    // Camera look target
    camera.setTarget(new BABYLON.Vector3(0, 3, 0));

    // 用一个大的平面作为场景地面
    const ground = BABYLON.MeshBuilder.CreateGround("ground", { width: 400, height: 400 }, scene);
    ground.position.y = 0;
    ground.checkCollisions = true;

    // 添加长条形状的障碍物
    const obstacles: BABYLON.Mesh[] = [];
    const obstacleData = [
        { x: 0, z: 30, w: 60, h: 4, rot: 0 },
        { x: -50, z: -20, w: 80, h: 4, rot: Math.PI / 4 },
        { x: 60, z: -60, w: 40, h: 4, rot: Math.PI / 2 },
        { x: 100, z: 50, w: 100, h: 4, rot: Math.PI / 8 },
    ];
    for (const { x, z, w, h, rot } of obstacleData) {
        const box = BABYLON.MeshBuilder.CreateBox("obstacle", { width: w, height: 6, depth: h }, scene);
        box.position = new BABYLON.Vector3(x, 3, z);
        box.rotation.y = rot;
        box.checkCollisions = true;
        const mat = new BABYLON.StandardMaterial("obstacleMat", scene);
        mat.diffuseColor = new BABYLON.Color3(0.3, 0.3, 0.3);
        box.material = mat;
        obstacles.push(box);
    }

    // Example sphere（恢复原始大小）
    const sphere = BABYLON.MeshBuilder.CreateSphere("sphere", { diameter: 2, segments: 32 }, scene);
    sphere.position.y = 1;
    sphere.scaling = new BABYLON.Vector3(1, 1, 1);
    sphere.ellipsoid = new BABYLON.Vector3(1, 1, 1);

    // 添加3个胶囊体NPC（恢复原始大小）
    const npcCapsules = [];
    const npcCharacters: Character[] = [];
    const npcHealthBars: HealthBar[] = [];
    const npcColors = [
        new BABYLON.Color3(1, 0, 1),
        new BABYLON.Color3(0, 1, 1),
        new BABYLON.Color3(1, 0.5, 0)
    ];
    const npcStartPositions = [
        new BABYLON.Vector3(-10, 2, -10),
        new BABYLON.Vector3(0, 2, 10),
        new BABYLON.Vector3(10, 2, -5)
    ];
    for (let i = 0; i < 3; i++) {
        const npc = BABYLON.MeshBuilder.CreateCapsule(`npcCapsule${i}`, { height: 4, radius: 1 }, scene);
        npc.position = npcStartPositions[i].clone();
        npc.checkCollisions = true;
        npc.scaling = new BABYLON.Vector3(1, 1, 1);
        npc.ellipsoid = new BABYLON.Vector3(1, 2, 1);
        const npcMat = new BABYLON.StandardMaterial(`npcMat${i}`, scene);
        npcMat.diffuseColor = npcColors[i];
        npc.material = npcMat;
        npcCapsules.push(npc);
        npcCharacters.push(new Character(`NPC${i+1}`, 100));
        // Add health bar for this NPC
        const hb = new HealthBar(scene, npc, { width: 1, height: 0.18, offsetY: 3 });
        hb.show();
        npcHealthBars.push(hb);
    }

    // 为每个NPC设定初始移动方向
    const npcDirections = [
        new BABYLON.Vector3(1, 0, 0),   // x方向
        new BABYLON.Vector3(0, 0, -1),  // -z方向
        new BABYLON.Vector3(-1, 0, 1).normalize() // 斜方向
    ];
    const npcSpeeds = [0.025, 0.035, 0.03]; // 每个NPC的速度

    const gravityY = scene.gravity.y; // -2

    // 记录NPC上一次的位置和未移动的帧数
    const npcLastPositions = npcCapsules.map(npc => npc.position.clone());
    const npcStuckFrames = [0, 0, 0];
    const STUCK_THRESHOLD = 30; // 连续多少帧未移动算卡住
    const STUCK_MOVE_DIST = 0.01; // 多少距离以内算没动
    const npcChangeDirTimers = [0, 0, 0];
    const CHANGE_DIR_INTERVAL = 60; // 平均每60帧尝试一次随机转向
    const CHANGE_DIR_PROB = 0.15; // 每次尝试有15%概率转向

    scene.registerBeforeRender(() => {
        for (let i = 0; i < npcCapsules.length; i++) {
            // 让NPC用moveWithCollisions移动，带重力
            const velocity = npcDirections[i].scale(npcSpeeds[i]);
            velocity.y = gravityY * scene.getEngine().getDeltaTime() / 1000; // 模拟重力
            npcCapsules[i].moveWithCollisions(velocity);

            // 简单边界检测，碰到边界就反向
            const pos = npcCapsules[i].position;
            if (pos.x < -15 || pos.x > 15) {
                npcDirections[i].x *= -1;
            }
            if (pos.z < -15 || pos.z > 15) {
                npcDirections[i].z *= -1;
            }

            // 检查是否卡住
            if (BABYLON.Vector3.Distance(pos, npcLastPositions[i]) < STUCK_MOVE_DIST) {
                npcStuckFrames[i]++;
                if (npcStuckFrames[i] > STUCK_THRESHOLD) {
                    // 随机改变方向
                    const angle = Math.random() * Math.PI * 2;
                    npcDirections[i].x = Math.cos(angle);
                    npcDirections[i].z = Math.sin(angle);
                    npcStuckFrames[i] = 0;
                }
            } else {
                npcStuckFrames[i] = 0;
            }
            npcLastPositions[i].copyFrom(pos);

            // 随机定时改变方向
            npcChangeDirTimers[i]++;
            if (npcChangeDirTimers[i] > CHANGE_DIR_INTERVAL) {
                if (Math.random() < CHANGE_DIR_PROB) {
                    const angle = Math.random() * Math.PI * 2;
                    npcDirections[i].x = Math.cos(angle);
                    npcDirections[i].z = Math.sin(angle);
                }
                npcChangeDirTimers[i] = 0;
            }
        }
    });

    // 平


    // --- 玩家控制与射击功能集成 ---
    // 按键状态
    const keys: { [key: string]: boolean } = {};

    // 监听键盘事件，更新keys对象
    window.addEventListener('keydown', (e) => {
        keys[e.code] = true;
    });
    window.addEventListener('keyup', (e) => {
        keys[e.code] = false;
    });

    // 玩家角色属性
    const playerCharacter = new Character('Player', 100);
    // 玩家体力条
    const playerStaminaBar = new HealthBar(scene, sphere, { width: 1.2, height: 0.18, offsetY: 2.2 });
    // 设置体力条为黄色
    (playerStaminaBar.bar.material as BABYLON.StandardMaterial).diffuseColor = BABYLON.Color3.Yellow();
    (playerStaminaBar.bar.material as BABYLON.StandardMaterial).emissiveColor = BABYLON.Color3.Yellow();
    (playerStaminaBar.container.material as BABYLON.StandardMaterial).diffuseColor = BABYLON.Color3.Red();
    playerStaminaBar.show();

    // 玩家射击相关属性
    const playerBullets: any[] = [];
    const playerMoveSpeed = 0.2;
    const playerMaxSpeed = 10;
    const playerSphere = sphere; // 你操控的球体
    playerSphere.rotation = new BABYLON.Vector3(0, 0, 0);

    // 创建武器
    const shooter = BABYLON.MeshBuilder.CreateBox('player_weapon', { width: 2, height: 0.5, depth: 0.5 }, scene);
    shooter.parent = playerSphere;
    shooter.position.z += 1;
    shooter.rotation.y += Math.PI / 2;
    const shooterMat = new BABYLON.StandardMaterial('player_shooterMat', scene);
    
    shooterMat.diffuseColor = new BABYLON.Color3(1, 0.4, 0);
    shooter.material = shooterMat;

    // 发光层
    const gl = new BABYLON.GlowLayer('gl', scene);

    // 射击函数
    function shoot() {
        const speedCharacter = 8;
        const bullet = BABYLON.MeshBuilder.CreateSphere('bullet', { diameter: 0.5 }, scene);
        bullet.position = shooter.getAbsolutePosition().clone();
        const bulletMat = new BABYLON.StandardMaterial('bullet_mat', scene);
        bulletMat.emissiveColor = BABYLON.Color3.Random();
        bullet.material = bulletMat;

        playerBullets.push(bullet);
        // 计算朝向
        const yRot = playerSphere.rotation.y;
        const forwards = new BABYLON.Vector3(
            Math.sin(yRot) / speedCharacter,
            0,
            Math.cos(yRot) / speedCharacter
        );
        // 子弹移动逻辑
        bullet.metadata = { velocity: forwards.scale(10) };
        setTimeout(() => {
            const idx = playerBullets.indexOf(bullet);
            if (idx !== -1) playerBullets.splice(idx, 1);
            bullet.dispose();
        }, 750);
    }

    // NPC重生函数（带2秒冷却）
    function respawnNPCWithDelay(i: number) {
        setTimeout(() => {
            const pos = new BABYLON.Vector3(
                (Math.random() - 0.5) * 30,
                2,
                (Math.random() - 0.5) * 30
            );
            const npc = BABYLON.MeshBuilder.CreateCapsule(`npcCapsule${Date.now()}_${i}`, { height: 4, radius: 1 }, scene);
            npc.position = pos;
            npc.checkCollisions = true;
            npc.ellipsoid = new BABYLON.Vector3(1, 2, 1);
            const npcMat = new BABYLON.StandardMaterial(`npcMat${Date.now()}_${i}`, scene);
            npcMat.diffuseColor = new BABYLON.Color3(Math.random(), Math.random(), Math.random());
            npc.material = npcMat;
            npcCapsules.push(npc);
            npcCharacters.push(new Character(`NPC${Date.now()}_${i}`, 100));
            // Add health bar for respawned NPC
            const hb = new HealthBar(scene, npc, { width: 1, height: 0.18, offsetY: 3 });
            hb.show();
            npcHealthBars.push(hb);
        }, 2000);
    }

    // 子弹更新
    scene.registerBeforeRender(() => {
        for (let b = playerBullets.length - 1; b >= 0; b--) {
            const bullet = playerBullets[b];
            if (bullet && bullet.metadata && bullet.metadata.velocity) {
                bullet.position.addInPlace(bullet.metadata.velocity);
                // 检查与NPC的碰撞
                for (let i = npcCapsules.length - 1; i >= 0; i--) {
                    const npc = npcCapsules[i];
                    const npcChar = npcCharacters[i];
                    const npcHB = npcHealthBars[i];
                    if (npc && bullet.intersectsMesh(npc, false)) {
                        npcChar.takeDamage(50);
                        bullet.dispose();
                        playerBullets.splice(b, 1);
                        if (!npcChar.isAlive) {
                            npc.dispose();
                            npcHB.dispose();
                            npcCapsules.splice(i, 1);
                            npcCharacters.splice(i, 1);
                            npcHealthBars.splice(i, 1);
                            respawnNPCWithDelay(i);
                        }
                        break; // 一颗子弹只命中一个NPC
                    }
                }
            }
        }
    });

    // Update NPC health bars every frame
    scene.registerBeforeRender(() => {
        for (let i = 0; i < npcHealthBars.length; i++) {
            const char = npcCharacters[i];
            const hb = npcHealthBars[i];
            if (char && hb) {
                hb.update(char.health / char.maxHealth);
            }
        }
    });

    // 玩家移动与射击控制
    let mouseDown = false;
    let lastPointerX = 0;
    let sphereYaw = 0;

    // 鼠标指向地面时让小球朝向鼠标
    canvas.addEventListener('pointermove', (e) => {
        const pickResult = scene.pick(scene.pointerX, scene.pointerY, (mesh) => mesh.name === 'ground');
        if (pickResult && pickResult.hit && pickResult.pickedPoint) {
            const target = pickResult.pickedPoint;
            const pos = sphere.position;
            const dx = target.x - pos.x;
            const dz = target.z - pos.z;
            if (dx !== 0 || dz !== 0) {
                sphere.rotation.y = Math.atan2(dx, dz);
            }
        }
    });

    // WASD控制小球移动，方向受球体朝向影响
    scene.registerBeforeRender(() => {
        let move = new BABYLON.Vector3(0, 0, 0);
        if (keys['KeyW'] || keys['ArrowUp']) move.z = 1;
        if (keys['KeyS'] || keys['ArrowDown']) move.z -= 1;
        if (keys['KeyA'] || keys['ArrowLeft']) move.x -= 1;
        if (keys['KeyD'] || keys['ArrowRight']) move.x += 1;
        if (move.lengthSquared() > 0) {
            move = move.normalize().scale(playerMoveSpeed);
            // 旋转到球体朝向
            move = BABYLON.Vector3.TransformCoordinates(move, BABYLON.Matrix.RotationY(0));
            sphere.moveWithCollisions(move);
        }
    });

    // 玩家体力消耗与恢复逻辑
    scene.registerBeforeRender(() => {
        // Shift/LeftShift 控制奔跑
        const isRunning = keys['ShiftLeft'] || keys['ShiftRight'];
        if (isRunning && !playerCharacter.isTired) {
            playerCharacter.consumeStamina(0.4); // 每帧消耗体力
        } else {
            playerCharacter.recoverStamina(0.2); // 每帧恢复体力
        }
        // 体力为0时，不能奔跑
        if (playerCharacter.isTired) {
            // 体力耗尽只能慢速
            // cc.setRunSpeed(6); // 删除
        } else {
            // cc.setRunSpeed(20); // 删除
        }
        // 更新体力条
        playerStaminaBar.update(playerCharacter.staminaPercent);
    });

    scene.registerBeforeRender(() => {
        // 只保留射击控制
        if (keys['Space']) {
            shoot();
            keys['Space'] = false; // 防止连续射击
        }
    });

    // --- navmesh 集成 ---
    const navMeshManager = new NavMeshManager(scene);
    // ground和障碍物作为navmesh输入
    const navMeshes: BABYLON.AbstractMesh[] = [ground, ...obstacles];
    navMeshManager.createNavMesh(navMeshes).then(() => {
        navMeshManager.showDebugNavMesh();
    });

    // --- NPC追踪玩家功能 ---
    // 路径点缓存
    const npcPaths: BABYLON.Vector3[][] = [[], [], []];
    // 当前路径点索引
    const npcPathIndices = [0, 0, 0];
    // 路径刷新间隔（帧）
    const PATH_REFRESH_INTERVAL = 30;
    let pathRefreshCounter = 0;

    scene.registerBeforeRender(() => {
        pathRefreshCounter++;
        if (pathRefreshCounter >= PATH_REFRESH_INTERVAL) {
            pathRefreshCounter = 0;
            for (let i = 0; i < npcCapsules.length; i++) {
                // 查询navmesh路径
                const from = npcCapsules[i].position;
                const to = sphere.position;
                const path = navMeshManager.findPath(from, to);
                if (path && path.length > 1) {
                    npcPaths[i] = path;
                    npcPathIndices[i] = 1; // 跳到第一个目标点
                }
            }
        }
        // NPC沿路径移动
        for (let i = 0; i < npcCapsules.length; i++) {
            const npc = npcCapsules[i];
            const path = npcPaths[i];
            let idx = npcPathIndices[i];
            if (path && path.length > 1 && idx < path.length) {
                const target = path[idx];
                const dir = target.subtract(npc.position);
                const dist = dir.length();
                if (dist > 0.1) {
                    const move = dir.normalize().scale(0.04); // 追踪速度减半
                    npc.moveWithCollisions(move);
                }
                if (dist < 0.3) {
                    npcPathIndices[i]++;
                }
            }
        }
    });

    // 摄像机跟随小球
    scene.registerBeforeRender(() => {
        camera.target.x = sphere.position.x;
        camera.target.z = sphere.position.z;
        camera.position.x = sphere.position.x;
        camera.position.z = sphere.position.z;
        // camera.position.y 保持不变，实现俯视跟随
    });

    return scene;
    }


    constructor(private canvas: HTMLCanvasElement) {

        
        this.engine = new BABYLON.Engine(this.canvas, true);
        this.scene = App.CreateScene(this.engine, this.canvas);

        this.engine.runRenderLoop(() => {
            this.scene.render();
        });

        window.addEventListener('resize', () => {
            this.engine.resize();
        });
    }

    
}