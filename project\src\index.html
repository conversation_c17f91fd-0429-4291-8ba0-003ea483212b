<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>是男人就下100层</title>
    <style>
        html, body {
            overflow: hidden;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #000;
            font-family: Arial, sans-serif;
        }
        #renderCanvas {
            width: 100%;
            height: 100%;
            touch-action: none;
        }
        #instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            pointer-events: none;
        }
        #gameTitle {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="gameTitle">是男人就下100层</div>
    <canvas id="renderCanvas"></canvas>
    <div id="instructions">
        操作说明:<br>
        - 左右方向键或A/D键: 移动<br>
        - 空格键: 开始/重新开始游戏<br>
    </div>
    <script src="/assets/recast.js"></script>
    <script src="/bundle.js"></script>
</body>
</html>