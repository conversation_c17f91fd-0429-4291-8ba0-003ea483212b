{"name": "babylon_typescript", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "webpack serve --open", "build": "webpack --mode production"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"babylonjs": "^8.7.0", "babylonjs-charactercontroller": "^0.4.4", "babylonjs-gui": "^8.8.0", "babylonjs-loaders": "^8.7.0", "babylonjs-materials": "^8.7.1", "cannon-es": "^0.20.0", "recast-detour": "^1.6.4"}, "devDependencies": {"html-webpack-plugin": "^5.6.3", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.99.8", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}