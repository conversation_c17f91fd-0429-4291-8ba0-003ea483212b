import * as BABY<PERSON><PERSON> from 'babylonjs';
// @ts-ignore
import Recast from 'recast-detour';

export class NavMeshManager {
    private navigationPlugin: any;
    private navmeshDebug: BABYLON.Mesh | null = null;

    constructor(private scene: BABYLON.Scene) {}

    async createNavMesh(meshes: BABYLON.AbstractMesh[], params?: Partial<any>) {
        // 默认参数
        const defaultParams = {
            cs: 2,
            ch: 2,
            walkableSlopeAngle: 35,
            walkableHeight: 1,
            walkableClimb: 1,
            walkableRadius: 1,
            maxEdgeLen: 12,
            maxSimplificationError: 1.3,
            minRegionArea: 8,
            mergeRegionArea: 20,
            maxVertsPerPoly: 6,
            detailSampleDist: 6,
            detailSampleMaxError: 1,
        };
        const parameters = { ...defaultParams, ...params };

        // 异步加载 Recast
        const recast = await Recast();
        this.navigationPlugin = new BABYLON.RecastJSPlugin(recast);
        this.navigationPlugin.createNavMesh(meshes, parameters);
    }

    showDebugNavMesh() {
        if (!this.navigationPlugin) return;
        if (this.navmeshDebug) {
            this.navmeshDebug.setEnabled(true);
            return;
        }
        this.navmeshDebug = this.navigationPlugin.createDebugNavMesh(this.scene);
        const matdebug = new BABYLON.StandardMaterial('matdebug', this.scene);
        matdebug.diffuseColor = new BABYLON.Color3(0.1, 0.2, 1);
        matdebug.alpha = 0.2;
        this.navmeshDebug.material = matdebug;
    }

    hideDebugNavMesh() {
        if (this.navmeshDebug) {
            this.navmeshDebug.setEnabled(false);
        }
    }

    getPlugin() {
        return this.navigationPlugin;
    }

    findPath(from: BABYLON.Vector3, to: BABYLON.Vector3): BABYLON.Vector3[] | null {
        if (!this.navigationPlugin) return null;
        // 查询路径
        const path = this.navigationPlugin.computePath(from, to);
        if (path && path.length > 0) {
            return path;
        }
        return null;
    }
} 