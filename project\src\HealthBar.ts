import * as BABY<PERSON><PERSON> from 'babylonjs';

export class HealthBar {
    public container: BABYLON.Mesh;
    public bar: BABYLON.Mesh;
    private maxWidth: number;
    private offsetY: number;

    constructor(scene: BABYLON.Scene, parent: BABYLON.Mesh, options?: { width?: number, height?: number, offsetY?: number }) {
        const width = options?.width ?? 1;
        const height = options?.height ?? 0.18;
        this.offsetY = options?.offsetY ?? 2.6;
        this.maxWidth = width;

        // Container (red background)
        this.container = BABYLON.MeshBuilder.CreatePlane('healthBarContainer', { width, height }, scene);
        const containerMat = new BABYLON.StandardMaterial('healthBarContainerMat', scene);
        containerMat.diffuseColor = BABYLON.Color3.Red();

        containerMat.backFaceCulling = true;
        this.container.material = containerMat;
        this.container.billboardMode = BABYLON.Mesh.BILLBOARDMODE_ALL;
        this.container.parent = parent;
        this.container.position = new BABYLON.Vector3(0, this.offsetY, 0);
        this.container.isPickable = false;
        this.container.renderingGroupId = 1;

        // Bar (green foreground)
        this.bar = BABYLON.MeshBuilder.CreatePlane('healthBar', { width, height: height * 0.7 }, scene);
        const barMat = new BABYLON.StandardMaterial('healthBarMat', scene);
        barMat.diffuseColor = BABYLON.Color3.Green();
        barMat.backFaceCulling = true;
        this.bar.material = barMat;
        this.bar.parent = this.container;
        this.bar.position = new BABYLON.Vector3(0, 0, -0.01); // Slightly in front
        this.bar.isPickable = false;
        this.bar.renderingGroupId = 1;
    }

    update(healthPercent: number) {
        // Clamp between 0 and 1
        healthPercent = Math.max(0, Math.min(1, healthPercent));
        this.bar.scaling.x = healthPercent;
        // Move bar so left edge stays at left of container
        this.bar.position.x = -(this.maxWidth * (1 - healthPercent)) / 2;
    }

    show() {
        this.container.isVisible = true;
        this.bar.isVisible = true;
    }

    hide() {
        this.container.isVisible = false;
        this.bar.isVisible = false;
    }

    dispose() {
        this.bar.dispose();
        this.container.dispose();
    }
}
